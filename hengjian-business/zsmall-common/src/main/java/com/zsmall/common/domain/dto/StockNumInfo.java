package com.zsmall.common.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 库存查询相关信息对象
 *
 * <AUTHOR>
 * @create 2021/8/4 15:45
 */
@Data
public class StockNumInfo {

    Integer quantity = 0;

    List<String> notExistWarehouse;

    public StockNumInfo(Integer quantity, List<String> notExistWarehouse) {
        this.quantity = quantity;
        this.notExistWarehouse = notExistWarehouse;
    }
}
