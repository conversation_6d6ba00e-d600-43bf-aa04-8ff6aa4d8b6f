package com.zsmall.order.entity.domain.vo;

import java.util.Date;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;



/**
 * 子订单物流跟踪单视图对象 order_item_tracking_record
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrderItemTrackingRecord.class)
public class OrderItemTrackingRecordVo implements Serializable {

    /**
     * 主订单编号
     */
    @ExcelProperty(value = "Order ID")
    private String orderNo;

    /**
     * ItemNo
     */
    @ExcelProperty(value = "Sku")
    private String productSkuCode;

    @ExcelProperty(value = "日期")
    private String orderTime;

    /**
     * 渠道类型
     */
    @ExcelProperty(value = "渠道")
    private String channelType;

    @ExcelProperty(value = "发货方式")
    private String logisticsType;

    @ExcelProperty(value = "承运商/跟踪单")
    private String carrierOrTrackingNo;

    @ExcelProperty(value = "订单状态")
    private String orderState;

    /**
     * 物流进度（物流状态异常、未发货、标签创建、已发货、派送中、已履约等）
     */
    @ExcelProperty(value = "物流动态")
    private String logisticsProgress;

    private static final long serialVersionUID = 1L;

    private String tenantId;
    /**
     * 主键
     */
    private Long id;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    private String sku;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 发货时间
     */
    private Date dispatchedTime;

    /**
     * 履约时间
     */
    private Date fulfillmentTime;

    /**
     * 物流承运商
     */
    private String logisticsCarrier;

    /**
     * 物流服务
     */
    private String logisticsService;
    private String logisticsName;

    /**
     * 物流跟踪单号
     */
    private String logisticsTrackingNo;

    /**
     * 出货仓库编号
     */
    private String warehouseCode;

    /**
     * 出货仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 跟踪单是否由系统托管轮询（0-否，1-是）
     */
    private Boolean systemManaged;

    /**
     * 调用第三方物流接口次数
     */
    private Long callingApi;

    /**
     * 初次查询物流失败后，二次确认日期
     */
    private String confirmDate;

    /**
     * 上次查询物流信息时间
     */
    private Date lastQueryTime;

    /**
     * 第三方平台查询结果代号
     */
    private String thirdPartyCode;

    /**
     * 第三方平台查询结果信息
     */
    private String thirdPartyMessage;

    /**
     * 第三方平台物流信息更新时间
     */
    private String thirdPartyDateTime;

    private Long channelId;
    private String channelAlias;
    // 渠道订单号
    private String channelOrderId;

    private String trackingRedirectUrl;

    private String orderItemState;
    private String fulfillmentProgress;


    private String channelOrderTime;
    private String channelOrderName;
    private String latestLogisticsTime;
    private String latestLogisticsDetail;


}
