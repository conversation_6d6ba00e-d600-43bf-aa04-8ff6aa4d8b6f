package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.biz.service.IAnnouncementService;
import com.zsmall.system.entity.domain.bo.announcement.AnnouncementBo;
import com.zsmall.system.entity.domain.dto.announcement.AnnouncementUnReadNum;
import com.zsmall.system.entity.domain.dto.announcement.BatchReadStatusDto;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo;
import com.zsmall.system.entity.mapper.AnnouncementMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 公告Controller
 * 包含管理员端和用户端的所有接口
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/announcement")
public class AnnouncementController extends BaseController {

    private final IAnnouncementService announcementService;
    private final AnnouncementMapper announcementMapper;

    /**
     * 新增公告
     */
    @Log(title = "公告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @Transactional(rollbackFor = Exception.class)
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AnnouncementBo bo) {
        if (!"Manager".equals(LoginHelper.getTenantType())) {
           return R.fail("非管理员租户类型，不能新增公告");
        }

        return toAjax(announcementService.insertByBo(bo));
    }

    /**
     * 分页查询公告列表（通用接口）
     * 管理员：查看所有公告（包含已禁用、已过期的公告，用于管理）
     * 分销商/供应商：查看适用于自己租户类型的有效公告（仅启用且未过期）
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     */
    @GetMapping("/list")
    @SaCheckLogin
    public Object list(AnnouncementBo bo, PageQuery pageQuery) {
        // 如果是弹窗查询，只查询当前租户类型的公告
        return announcementService.queryPageList(bo, pageQuery);
    }

    /**
     * 弹窗列表查询（仅用于前端弹窗选择）
     *
     */
    @GetMapping("/popWindowsList")
    @SaCheckLogin
    public Object popWindowsList() {
        List<AnnouncementVo> r = announcementService.popWindowSearch(LoginHelper.getTenantId());
        return r;
    }

    /**
     * 获取公告详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    @SaCheckLogin
    public R<AnnouncementVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(announcementService.queryById(id));
    }



    /**
     * 修改公告
     */
    @Log(title = "公告", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    @SaCheckLogin
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AnnouncementBo bo) {
        return toAjax(announcementService.updateByBo(bo));
    }

    /**
     * 删除公告（智能删除）
     * 管理员：所有租户不可见（全局删除）
     * 分销商/供应商：仅对当前租户不可见（租户级删除）
     */
    @Log(title = "公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @SaCheckLogin
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        if ("Manager".equals(LoginHelper.getTenantType())) {
            // 管理员删除：全局删除，所有租户不可见
            return toAjax(announcementService.deleteWithValidByIds(Arrays.asList(ids)));
        } else {
            // 分销商/供应商删除：仅对当前租户不可见
            return toAjax(announcementService.deleteForTenant(Arrays.asList(ids), LoginHelper.getTenantId()));
        }
    }

    /**
     * 更新公告状态（启用/停用）
     * @param announcementIds 公告ID列表
     * @param status 状态（0：启用，1：停用）
     */
    @Log(title = "公告状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{status}")
    @SaCheckLogin
    public R<Void> updateStatus(@RequestBody List<Long> announcementIds,@PathVariable Integer status) {
        if (announcementIds == null || announcementIds.isEmpty()) {
            return R.fail("参数不能为空");
        }
        announcementService.updateStatus(announcementIds,status);
        return R.ok();
    }


    /**
     * 批量更新已读状态
     * @param dto 包含公告ID列表和已读状态的请求对象
     */
    @PutMapping("/messages/batchRead")
    @SaCheckLogin
    public R<Void> batchUpdateReadStatus(@Validated @RequestBody BatchReadStatusDto dto) {
        if (dto.getStatus() == 1) {
            return toAjax(announcementService.batchMarkRead(dto.getAnnouncementIds(), LoginHelper.getTenantId()));
        } else {
            return toAjax(announcementService.batchMarkUnread(dto.getAnnouncementIds(), LoginHelper.getTenantId()));
        }
    }

    /**
     * 标记弹窗已读
     * @param announcementId 包含公告ID
     */
    @PutMapping("/messages/batchPopWindows/{announcementId}")
    @SaCheckLogin
    public R<Void> batchPopWindows(@PathVariable Long announcementId) {
        announcementService.batchPopWindows(Collections.singletonList(announcementId), LoginHelper.getTenantId());
        return R.ok();
    }

    /**
     * 获取公告/预警等数量
     */
    @GetMapping("/unreadCount")
    @SaCheckLogin
    public R getUnreadCount() {
        Long annCount = announcementService.countAnnonUnreadMessages(LoginHelper.getTenantId());
        Long skuCount= TenantHelper.ignore(()->announcementMapper.countWarningSkuUnreadMessages(LoginHelper.getTenantId()));
        AnnouncementUnReadNum a = new AnnouncementUnReadNum();
        a.setAnnouncementNum(annCount);
        a.setWarningSkuTotalNum(skuCount);
        a.setTotalNum(annCount+skuCount);
        return R.ok(a);
    }


}
