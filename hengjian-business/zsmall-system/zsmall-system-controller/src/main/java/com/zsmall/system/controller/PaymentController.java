package com.zsmall.system.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.biz.service.PaymentExtensionService;
import com.zsmall.system.biz.service.PaymentMethodService;
import com.zsmall.system.entity.domain.OrdersSystemEntity;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.transaction.TransactionOrderBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionOrderRecordBo;
import com.zsmall.system.entity.domain.vo.payment.PaymentMethodListVo;
import com.zsmall.system.entity.domain.vo.payment.RechargeRecordVo;
import com.zsmall.system.entity.domain.vo.payment.TransactionOrderRecordVo;
import com.zsmall.system.entity.domain.vo.transaction.RechargeReceiptStatsVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionOrderDetailVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionRecordVo;
import com.zsmall.system.entity.iservice.ITransactionReceiptService;
import com.zsmall.system.entity.mapper.TransactionRecordMapper;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易相关-控制层
 *
 * @date 2023年6月13日
 */
@Slf4j
@RestController
@RequestMapping(value = "/payment")
@Tag(name = "交易相关接口")
public class PaymentController {

    @Autowired
    private PaymentMethodService paymentMethodService;
    @Autowired
    private ITransactionReceiptService iTransactionReceiptService;
    @Autowired
    private PaymentExtensionService paymentExtensionService;
    @Autowired
    private TransactionRecordMapper transactionRecordMapper;

    /**
     * 钱包总览
     *
     * @param obj
     * @return
     */
    @PostMapping(value = "/getPaymentMethodHome")
    public R<PaymentMethodListVo> getPaymentMethodHome(@RequestBody Object obj) {
        return paymentMethodService.getPaymentMethodHome(obj);
    }

    /**
     * 翻页查询充值记录列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping(value = "/getWalletDetailsPage")
    public R<RechargeRecordVo> getWalletDetailsPage(RechargeRecordVo.RechargeRecordDetails bo, PageQuery pageQuery) {
        return paymentMethodService.getWalletDetailsPage(bo, pageQuery);
    }

    /**
     * 查询充值详情
     *
     * @param receiptNo
     * @return
     */
    @RequestMapping(value = "/getRechargeDetail/{receiptNo}", method = RequestMethod.GET)
    public R<RechargeRecordVo.RechargeRecordDetails> getRechargeRecordDetails(@PathVariable String receiptNo) {
        return paymentExtensionService.getRechargeRecordDetails(receiptNo);
    }

    /**
     * 导出充值记录明细
     */
    @Log(title = "充值记录明细", businessType = BusinessType.EXPORT)
    @PostMapping("/rechargeRecord/export")
    public R<Void> export(@RequestBody RechargeRecordVo.RechargeRecordDetails bo, HttpServletResponse response) {
//        List<TransactionReceiptVo> list = paymentMethodService.queryList(bo);
//        ExcelUtil.exportExcelWithLocale(list, "充值记录明细", TransactionReceiptVo.class, response);
        paymentMethodService.export(bo);
        return R.ok();
    }

    /**
     * 翻页查询交易记录列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping(value = "/getDistrTransactionsPage")
    public R<TransactionOrderRecordVo> getDistrTransactionsPage(TransactionOrderRecordBo bo, PageQuery pageQuery) {
        if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Manager.name())){
            bo.setTenantId(LoginHelper.getTenantId());
        }
        return paymentMethodService.getDistrTransactionsPage(bo, pageQuery);
    }

    /**
     * 查询交易记录详情
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping(value = "/getTransactionOrder")
    public R<TableDataInfo<TransactionOrderDetailVo>> getTransactionOrder(TransactionOrderBo bo, PageQuery pageQuery) {
        return paymentMethodService.getTransactionOrder(bo, pageQuery);
    }

    /**
     * 导出交易记录
     */
    @Log(title = "交易总览-明细导出", businessType = BusinessType.EXPORT)
    @GetMapping("/transactionOverview/export")
    public R transactionOverviewExport(TransactionOrderRecordBo bo, HttpServletResponse response) {
        bo.setTenantId(LoginHelper.getTenantId());
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.TRANSACTION_OVERVIEW_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
    DownloadRecordUtil.generate(
        fileName,
        DownloadTypePlusEnum.TransactionOverviewExport,
        tempFileSavePath -> {
          PageQuery pageQuery = new PageQuery();
          pageQuery.setPageNum(0);
          pageQuery.setPageSize(1);

          LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
          // 交易时间
          List<String> tradingDates = bo.getTradingDates();
          if (CollUtil.isNotEmpty(tradingDates)) {
            Date sDate = DateUtil.parseDate(tradingDates.get(0));
            Date eDate = DateUtil.parseDate(tradingDates.get(1));
            lqw.ge(TransactionRecord::getTransactionTime, DateUtil.beginOfDay(sDate))
                .le(TransactionRecord::getTransactionTime, DateUtil.endOfDay(eDate));
          }
          if (StrUtil.isNotEmpty(bo.getTenantId())) {
            lqw.eq(TransactionRecord::getTenantId, bo.getTenantId());
          }
          // 交易类型
          String transactionType = bo.getTransactionType();
          if (StrUtil.isNotBlank(transactionType)) {
            lqw.eq(
                TransactionRecord::getTransactionType,
                TransactionTypeEnum.valueOf(transactionType));
          } else {
            List<TransactionTypeEnum> trs = new ArrayList<>();
            trs.add(TransactionTypeEnum.Income);
            trs.add(TransactionTypeEnum.Expenditure);
            lqw.in(TransactionRecord::getTransactionType, trs);
          }
          if (StrUtil.isNotEmpty(bo.getTransactionNo())) {
            lqw.eq(TransactionRecord::getTransactionNo, bo.getTransactionNo());
          }
          if (StrUtil.isNotEmpty(bo.getCurrencyCode())) {
            lqw.eq(TransactionRecord::getCurrency, bo.getCurrencyCode());
          }
          lqw.orderByDesc(TransactionRecord::getId);
          if (StrUtil.isNotEmpty(bo.getOrderNo())) {
            String sql =
                "SELECT 1\n"
                    + "              FROM (SELECT too.transactions_id\n"
                    + "                    FROM transactions_orders too\n"
                    + "                             INNER JOIN orders o ON too.order_id = o.id\n"
                    + "                    WHERE too.transactions_id = transaction_record.id\n"
                    + "                      AND o.order_no = {0}\n"
                    + "                    UNION ALL\n"
                    + "                    SELECT tor.transactions_id\n"
                    + "                    FROM transactions_order_refund tor\n"
                    + "                             INNER JOIN order_refund orr ON tor.order_refund_id = orr.id\n"
                    + "                             INNER JOIN orders o ON orr.order_id = o.id\n"
                    + "                    WHERE tor.transactions_id = transaction_record.id\n"
                    + "                      AND o.order_no = {0}) AS subquery\n"
                    + "              WHERE subquery.transactions_id = transaction_record.id";
            lqw.exists(sql, bo.getOrderNo());
          }
          Page<TransactionRecord> page = TenantHelper.ignore(() -> transactionRecordMapper.selectPage(pageQuery.build(), lqw));
            // 根据total分页
          int rowCount = Math.toIntExact(page.getTotal());
          // 一次查询的数据量
          int pageSize = 5000;
          // 总页数
          int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);
          List<TransactionRecordVo> t = new ArrayList<>();
          for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
            // 计算当前页的起始行
            int startRow = (currentPage - 1) * pageSize;
            // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
            if (currentPage == totalPage && rowCount % pageSize != 0) {
              pageSize = rowCount % pageSize;
            }
           PageQuery pageQuerys = new PageQuery();
              pageQuerys.setPageNum(startRow);
              pageQuerys.setPageSize(pageSize);
              Page<TransactionRecord> recordPage = TenantHelper.ignore(() -> transactionRecordMapper.selectPage(pageQuerys.build(), lqw));
              List<TransactionRecord> recordList = recordPage.getRecords();
              Set<String> transactionNos = recordList.stream().map(TransactionRecord::getTransactionNo).collect(Collectors.toSet());
              if (CollUtil.isNotEmpty(transactionNos)){
                  List<OrdersSystemEntity> orderList = transactionRecordMapper.queryOrdersByOrderNo(transactionNos);
                  Map<String, OrdersSystemEntity> map = orderList.stream()
                                                      .collect(Collectors.toMap(OrdersSystemEntity::getChannelOrderNo, order -> order));
                  List<TransactionRecordVo> transactionRecordVos = BeanUtil.copyToList(recordList, TransactionRecordVo.class);
                  transactionRecordVos.forEach(s->{
                      OrdersSystemEntity orders = map.get(s.getTransactionNo());
                      if (orders != null) {
                          s.setOrderNo(orders.getOrderNo());
                      }
                  });
                  t.addAll(transactionRecordVos);
              }
          }
          File tempFile = new File(tempFileSavePath);
          BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
          ExcelUtil.exportExcelWithLocale(t, "TransactionOverView", TransactionRecordVo.class, false, outputStream, headerLocale);
          IoUtil.close(outputStream);
          return tempFile;
        });
        return R.ok();
    }

    /**
     * 查询充值统计相关
     *
     * @return
     */
    @GetMapping("/getRechargeReceiptStatistics")
    public R<RechargeReceiptStatsVo> getRechargeReceiptStatistics(@RequestParam("currency")String currency) {
        RechargeReceiptStatsVo staffReceiptStatsVo = iTransactionReceiptService.getRechargeStatistics(currency);
        return R.ok(staffReceiptStatsVo);
    }

}
