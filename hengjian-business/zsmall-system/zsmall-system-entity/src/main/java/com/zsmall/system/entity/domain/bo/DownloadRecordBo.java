package com.zsmall.system.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.system.entity.domain.DownloadRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 下载记录业务对象 download_record
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DownloadRecord.class, reverseConvertGenerate = false)
public class DownloadRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 过期时间
     */
    @NotNull(message = "过期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expiredTime;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 文件大小
     */
    @NotBlank(message = "文件大小不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileSize;

    /**
     * 文件存储key
     */
    @NotBlank(message = "文件存储key不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileSaveKey;

    /**
     * 下载文件查询条件
     */
    @NotBlank(message = "下载文件查询条件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String downloadQuery;

    /**
     * 下载类型（例：商品资料包、交易记录、订单等）
     */
    @NotBlank(message = "下载类型（例：商品资料包、交易记录、订单等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String downloadType;

    /**
     * 状态：1-生成中，2-已准备下载，3-过期
     */
    @NotNull(message = "状态：1-生成中，2-已准备下载，3-过期不能为空", groups = { AddGroup.class, EditGroup.class })
    private RecordStateEnum recordState;


}
