package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.WarningMessageTenant;
import com.zsmall.system.entity.domain.vo.warningMessage.WarningMessageTenantVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 告警租户表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface WarningMessageTenantMapper extends BaseMapperPlus<WarningMessageTenant, WarningMessageTenantVo> {

    /**
     * 根据预警ID和租户ID查询记录
     * @param warningMessageId 预警ID
     * @param tenantId 租户ID
     * @return 告警租户记录
     */
    WarningMessageTenant selectByWarningMessageAndTenant(@Param("warningMessageId") Long warningMessageId, @Param("tenantId") String tenantId);

    /**
     * 批量插入告警租户记录
     * @param list 告警租户记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<WarningMessageTenant> list);

    /**
     * 批量更新已读状态
     * @param tenantId 租户ID
     * @param warningMessageIds 预警ID列表
     * @param isRead 是否已读
     * @return 更新数量
     */
    int batchUpdateReadStatus(@Param("tenantId") String tenantId, @Param("warningMessageIds") List<Long> warningMessageIds, @Param("isRead") Integer isRead);

    /**
     * 根据预警ID删除记录
     * @param warningMessageId 预警ID
     * @return 删除数量
     */
    int deleteByWarningMessageId(@Param("warningMessageId") Long warningMessageId);

}
