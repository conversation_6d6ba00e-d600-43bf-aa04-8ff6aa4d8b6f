<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.WarningMessageTenantMapper">

    <!-- 根据预警ID和租户ID查询记录 -->
    <select id="selectByWarningMessageAndTenant" resultType="com.zsmall.system.entity.domain.WarningMessageTenant">
        SELECT
            id,
            warning_message_id,
            tenant_id,
            is_read,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time
        FROM warning_message_tenant
        WHERE warning_message_id = #{warningMessageId} AND tenant_id = #{tenantId}
        AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 批量插入告警租户记录 -->
    <insert id="batchInsert">
        INSERT INTO warning_message_tenant (warning_message_id, tenant_id, is_read, del_flag, create_by, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.warningMessageId}, #{item.tenantId}, #{item.isRead}, #{item.delFlag}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 批量更新已读状态 -->
    <update id="batchUpdateReadStatus">
        UPDATE warning_message_tenant
        SET is_read = #{isRead}, update_time = NOW()
        WHERE tenant_id = #{tenantId}
        AND del_flag = 0
        AND warning_message_id IN
        <foreach collection="warningMessageIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据预警ID删除记录 -->
    <delete id="deleteByWarningMessageId">
        UPDATE warning_message_tenant
        SET del_flag = 2, update_time = NOW()
        WHERE warning_message_id = #{warningMessageId}
    </delete>

</mapper>
