package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.*;
import com.zsmall.system.biz.service.PaymentExtensionService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.entity.domain.TenantPayoneer;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionReceiptAttachment;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.payment.PaymentExtensionBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptReviewBo;
import com.zsmall.system.entity.domain.vo.extraSetting.TenantPayoneerVo;
import com.zsmall.system.entity.domain.vo.payment.RechargeRecordVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionReceiptExcelVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionReceiptVo;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.mapper.TransactionReceiptAttachmentMapper;
import com.zsmall.system.entity.mapper.TransactionReceiptMapper;
import com.zsmall.system.entity.mapper.TransactionRecordMapper;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易扩展实现类
 *
 * <AUTHOR> @create 2023年6月16日
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentExtensionServiceImpl implements PaymentExtensionService {

    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final TransactionRecordMapper transactionRecordMapper;
    private final TransactionReceiptMapper transactionReceiptMapper;
    private final TransactionReceiptAttachmentMapper transactionReceiptAttachmentMapper;
    private final ITenantPayoneerService iTenantPayoneerService;
    private final ITransactionReceiptService iTransactionReceiptService;
    private final ITransactionRecordService iTransactionRecordService;
    private final TenantWalletService tenantWalletService;
    private final ITenantWalletService iTenantWalletService;
    private final IBillService iBillService;
    private final ISysTenantService sysTenantService;

    private static final DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");


    /**
     * 获取汇款信息
     *
     * @param obj
     * @return
     */
    @Override
    public R<JSONObject> getRemittanceInfo(Object obj) {
        log.info("调用【获取汇款信息】接口 = {}", JSONUtil.toJsonStr(obj));
        JSONObject respBody = new JSONObject();
        List<TenantPayoneerVo> tenantPayoneerList = iTenantPayoneerService.getTenantPayoneerList(null);
        if (CollUtil.isNotEmpty(tenantPayoneerList)) {
            TenantPayoneerVo tenantPayoneerVo = tenantPayoneerList.get(0);
            respBody.putOpt("payoneer", tenantPayoneerVo);
        }

        // TODO 银行账户信息

        return R.ok(respBody);
    }

    /**
     * 提交交易回执单
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Void> submitPaymentReceipt(PaymentExtensionBo bo) throws Exception {
        log.info("调用【提交交易回执单】接口 = {}", JSONUtil.toJsonStr(bo));

        String tenantId;
        if(StringUtils.isEmpty(bo.getTenantId())){
            LoginUser loginUser = LoginHelper.getLoginUser();
            tenantId = loginUser.getTenantId();
        }else {
            tenantId = bo.getTenantId();
        }

        String reqTransactionType = bo.getTransactionType();
        String reqPaymentMethodType = bo.getPaymentMethodType();
        TransactionTypeEnum transactionType = TransactionTypeEnum.valueOf(reqTransactionType);
        TransactionMethodEnum paymentMethodType = TransactionMethodEnum.valueOf(reqPaymentMethodType);

        if (ObjectUtil.equal(transactionType, TransactionTypeEnum.Recharge)) {
            String reqPaymentDate = bo.getPaymentDate();
            String reqAccountId = bo.getAccountId();
            String reqNote = bo.getNote();
            BigDecimal amount = bo.getAmount();
            String fileSavePath = bo.getFileSavePath();
            String fileShowUrl = bo.getFileShowUrl();
            String fileName = bo.getShowFileVal();
            String fileType = bo.getFileType();

            TransactionReceipt transactionReceipt = new TransactionReceipt();
            String transactionReceiptNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionReceiptNo);
            transactionReceipt.setTransactionReceiptNo(transactionReceiptNo);
            transactionReceipt.setTenantId(tenantId);
            transactionReceipt.setAccountId(reqAccountId);
            transactionReceipt.setTransactionMethod(paymentMethodType);
            transactionReceipt.setTransactionType(transactionType);
            transactionReceipt.setTransactionTime(DateUtil.parseLocalDateTime(reqPaymentDate, "yyyy-MM-dd HH:mm:ss"));
            transactionReceipt.setTransactionAmount(amount);
            transactionReceipt.setNote(reqNote);
            transactionReceipt.setCurrency(bo.getCurrency());
            transactionReceipt.setCurrencySymbol(bo.getCurrencySymbol());
            transactionReceipt.setReviewState(ReceiptReviewStateEnum.Pending);

            if (ObjectUtil.equal(paymentMethodType, TransactionMethodEnum.OfflinePayoneer)
                || ObjectUtil.equal(paymentMethodType, TransactionMethodEnum.DirectBankTransfer)) {
                String accountName = bo.getAccountName();
                transactionReceipt.setAccountName(accountName);
            }

            TransactionRecord transactions = new TransactionRecord();
            String transactionNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo);
            transactions.setTransactionNo(transactionNo);
            transactions.setTransactionType(transactionType);
            transactions.setTransactionSubType(TransactionSubTypeEnum.Recharge);
            transactions.setTransactionAmount(amount);
            transactions.setTransactionState(TransactionStateEnum.Processing);
            transactions.setTransactionNote(reqNote);
            transactions.setAfterBalance(new BigDecimal("0.00"));
            transactions.setBeforeBalance(new BigDecimal("0.00"));
            transactions.setCurrency(bo.getCurrency());
            transactions.setCurrencySymbol(bo.getCurrencySymbol());
            transactions.setTenantId(tenantId);
            transactionRecordMapper.insert(transactions);

            transactionReceipt.setTransactionsId(transactions.getId());
            transactionReceiptMapper.insert(transactionReceipt);
            //添加附件保存操作
            if (StrUtil.isNotBlank(fileShowUrl)) {
                TransactionReceiptAttachment att = new TransactionReceiptAttachment();
                att.setTransactionReceiptNo(transactionReceipt.getTransactionReceiptNo());
                att.setAttachmentOriginalName(fileName);
                att.setAttachmentName(fileName);
                att.setAttachmentSavePath(fileSavePath);
                att.setAttachmentShowUrl(fileShowUrl);
                att.setAttachmentSuffix(fileType);
                att.setOssId(bo.getFileOssId());
                att.setAttachmentType(
                    (StrUtil.equals(fileType, "pdf") || StrUtil.equals(fileType, "PDF")) ? AttachmentTypeEnum.File.getValue() : AttachmentTypeEnum.Image.getValue()
                );
                if (transactionReceiptAttachmentMapper.insert(att) <= 0) {
                    throw new Exception("附件添加失败");
                }
            }
        } else if (ObjectUtil.equal(transactionType, TransactionTypeEnum.Withdrawal)) {
        } else {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        return R.ok();
    }


    @Override
    public R<RechargeRecordVo.RechargeRecordDetails> getRechargeRecordDetails(String receiptNo) {
        log.info("进入【获取充值详情】方法，{}", receiptNo);
        TransactionReceipt transactionReceipt = iTransactionReceiptService.selectByReceiptNoWithNotTenant(receiptNo);

        RechargeRecordVo.RechargeRecordDetails vo = new RechargeRecordVo.RechargeRecordDetails();
        ReceiptReviewStateEnum reviewState = transactionReceipt.getReviewState();
        String accountId = transactionReceipt.getAccountId();
        String tenantId = transactionReceipt.getTenantId();

        vo.setTenantId(tenantId);
        vo.setCurrency(transactionReceipt.getCurrency());
        vo.setCurrencySymbol(transactionReceipt.getCurrencySymbol());
        vo.setTransactionTime(LocalDateTimeUtil.format(transactionReceipt.getTransactionTime(), dateTimeFormatter));
        vo.setTransactionAmount(transactionReceipt.getTransactionAmount());
        vo.setTransactionFee(transactionReceipt.getTransactionFee());
        vo.setTransactionNo(receiptNo);
        vo.setTransactionMethod(transactionReceipt.getTransactionMethod().name());
        vo.setRemark(transactionReceipt.getNote());
        vo.setTransactionState(reviewState.getValue());
        vo.setAccountName(transactionReceipt.getAccountName());
        vo.setAccountId(accountId);
        vo.setReceiptTime(LocalDateTimeUtil.format(transactionReceipt.getReceiptTime(), dateTimeFormatter));

        // 特殊处理
        if(ObjectUtil.equal(transactionReceipt.getTransactionMethod(), TransactionMethodEnum.OnlinePayoneer)) {
            TenantPayoneer tenantPayoneer = iTenantPayoneerService.selectByAccountIdAndTenantId(tenantId, accountId);
            if(tenantPayoneer != null) {
                vo.setAccountName(tenantPayoneer.getAccountName());
            }
        }

        // 附件
        TransactionReceiptAttachment transactionReceiptAttachment = iTransactionReceiptService.getAttachmentShowUrl(receiptNo);
        if(transactionReceiptAttachment != null) {
            vo.setAttachmentShowUrl(transactionReceiptAttachment.getAttachmentShowUrl());
            vo.setAttachmentFileType(transactionReceiptAttachment.getAttachmentType());
            vo.setAttachmentName(transactionReceiptAttachment.getAttachmentName());
        }


        return R.ok(vo);
    }

    /**
     * 审核支付回执单
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean reviewPaymentReceipt(TransactionReceiptReviewBo bo) throws Exception {
        String message = bo.getMessage();
        String reviewState = bo.getReviewState();
        String transactionReceiptNo = bo.getTransactionReceiptNo();

        TransactionReceipt transactionReceipt = iTransactionReceiptService.selectByReceiptNoWithNotTenant(transactionReceiptNo);
        if (transactionReceipt == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYMENT_RECEIPT_NOT_EXIST);
        }
        if (!ObjectUtil.equal(transactionReceipt.getReviewState(), ReceiptReviewStateEnum.Pending)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYMENT_RECEIPT_SOLVED);
        }


        ReceiptReviewStateEnum reviewStateType = ReceiptReviewStateEnum.valueOf(reviewState);
        TransactionTypeEnum transactionType = transactionReceipt.getTransactionType();

        Long transactionsId = transactionReceipt.getTransactionsId();
        TransactionRecord transactionRecord = transactionRecordMapper.selectByIdNotTenant(transactionsId);
        String tenantId = transactionRecord.getTenantId();
        // 且审核通过
        if (ObjectUtil.equal(reviewStateType, ReceiptReviewStateEnum.Accepted)) {
            transactionReceipt.setReceiptTime(LocalDateTime.now());
            tenantWalletService.walletChanges(tenantId, transactionRecord, true);
        }

        if(ObjectUtil.equal(reviewStateType, ReceiptReviewStateEnum.Rejected)) {
            message = StrUtil.isBlank(message) ? "Rejected by the administrator" : message;
//            transactionRecord.setTransactionNote(message);
            // 提现，驳回回滚钱包金额
            if (ObjectUtil.equal(transactionType, TransactionTypeEnum.Withdrawal)) {
                tenantWalletService.walletChanges(tenantId, transactionRecord, true);
            } else {
                transactionRecord.setTransactionState(TransactionStateEnum.Failure);
                iTransactionRecordService.insertOrUpdateByEntity(transactionRecord);
            }
        }

        transactionReceipt.setReviewState(reviewStateType);
        transactionReceipt.setNoteManager(message);
        iTransactionReceiptService.updateByIdNotTenant(transactionReceipt);
        return true;
    }

    @Override
    public void exportReceiptList(TransactionReceiptBo bo) {
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.RECHARGE_RECORD_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.RechargeRecordExport, tempFileSavePath -> {
            List<TransactionReceiptVo> list = iTransactionReceiptService.queryList(bo);
            List<String> tenantIds = list.stream().map(TransactionReceiptVo::getTenantId).distinct().collect(Collectors.toList());
            Map<String, SysTenant> tenantMapByTenantIds = sysTenantService.getTenantMapByTenantIds(tenantIds);
            list.parallelStream().forEach(refundApplyVo -> {
                SysTenant sysTenant = tenantMapByTenantIds.get(refundApplyVo.getTenantId());
                if (ObjectUtil.isNotNull(sysTenant)){
                    refundApplyVo.setThirdChannelFlag(sysTenant.getThirdChannelFlag());
                }
            });
//            List<TransactionReceiptVo> transactionReceiptVoList = queryList(bo);
            List<TransactionReceiptExcelVo> transactionReceiptExcelVoList = new ArrayList<>();
            for (TransactionReceiptVo transactionReceiptVo : list) {
                TransactionReceiptExcelVo transactionReceiptExcelVo = BeanUtil.copyProperties(transactionReceiptVo, TransactionReceiptExcelVo.class);
                if(null != transactionReceiptVo.getTransactionAmount()){
                    transactionReceiptExcelVo.setTransactionAmountString(transactionReceiptVo.getCurrencySymbol()+ NumberUtil.toStr(transactionReceiptVo.getTransactionAmount()));
                }else {
                    transactionReceiptExcelVo.setTransactionAmountString(transactionReceiptVo.getCurrencySymbol()+"0.00");
                }
                if(null != transactionReceiptVo.getTransactionFee()){
                    transactionReceiptExcelVo.setTransactionFeeString(transactionReceiptVo.getCurrencySymbol()+NumberUtil.toStr(transactionReceiptVo.getTransactionFee()));
                }else {
                    transactionReceiptExcelVo.setTransactionFeeString(transactionReceiptVo.getCurrencySymbol()+"0.00");
                }
                transactionReceiptExcelVoList.add(transactionReceiptExcelVo);
            }
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(transactionReceiptExcelVoList, "充值记录", TransactionReceiptExcelVo.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
    }

    /**
     * 填充导出充值交易回执单数据
     *
     * @param list
     * @param titleNames
     * @return
     */
    private List<Map<String, Object>> exportDepositRecordDataFill(List<PaymentExtensionBo> list, List<String> titleNames) {
        List<Map<String, Object>> rows = new ArrayList<>();
        list.stream().forEach(rd -> {
            Map<String, Object> map = new LinkedHashMap<>();
            //申请时间、到账时间、分销商、充值方式、转账账户名称、交易id、金额、备注、状态
            String paymentReceiptNo = rd.getPaymentReceiptNo();
            String paymentChannelNo = rd.getPaymentChannelNo();
            String createDate = rd.getCreateDate();
            String paymentDate = rd.getPaymentDate();
            String receiptDate = rd.getReceiptDate();
            String customerCode = rd.getCustomerCode();
            String transactionType = rd.getTransactionType();
            String paymentMethodType = rd.getPaymentMethodType();
            String accountId = rd.getAccountId();
            String accountName = rd.getAccountName();
            String bankName = rd.getBankName();
            String swiftCode = rd.getSwiftCode();
            String note = rd.getNote();
            BigDecimal amount = rd.getAmount();
            String showAmount = rd.getShowAmount();
            String fileSavePath = rd.getFileSavePath();
            String fileShowUrl = rd.getFileShowUrl();
            String status = rd.getStatus();
            map.put(titleNames.get(0), createDate);
            map.put(titleNames.get(1), receiptDate);
            map.put(titleNames.get(2), customerCode);
            map.put(titleNames.get(3), paymentMethodType);
            map.put(titleNames.get(4), accountName);
            map.put(titleNames.get(5), paymentReceiptNo);
            map.put(titleNames.get(6), showAmount);
            map.put(titleNames.get(7), note);
            map.put(titleNames.get(8), status);
            rows.add(map);
        });
        return rows;
    }


}
