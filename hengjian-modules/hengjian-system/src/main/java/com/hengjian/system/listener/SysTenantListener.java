package com.hengjian.system.listener;

import com.hengjian.extend.event.SysTenantQueryEvent;
import com.hengjian.extend.event.SysTenantUpdateEvent;
import com.hengjian.extend.event.TenantInfoEvent;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户相关事件监听
 *
 * <AUTHOR>
 * @date 2023/7/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysTenantListener {

    private final ISysTenantService iSysTenantService;

    /**
     * 事件监听 - 租户查询监听
     * @param event
     */
    @EventListener
    public void listenSysTenantQuery(SysTenantQueryEvent event) {
        SysTenantBo bo = new SysTenantBo();
        bo.setTenantType(event.getInput().name());
        List<SysTenantVo> sysTenantVoList = iSysTenantService.queryList(bo);
        event.setOutput(sysTenantVoList);
    }


    /**
     * 事件监听 - 查询租户相关信息
     *
     * @param tenantInfoEvent 租户信息事件
     */
    @EventListener
    public void sysTenantEventListener(TenantInfoEvent tenantInfoEvent) {
        String tenantId = tenantInfoEvent.getInTenantId();
        SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(tenantId);

        if (sysTenantVo != null) {
            tenantInfoEvent.setOutSysTenant(sysTenantVo);
        }
    }

    /**
     * 更改租户信息
     * @param sysTenantUpdateEvent
     */
    @EventListener
    public void sysTenantUpdateEventListener(SysTenantUpdateEvent sysTenantUpdateEvent) {
        SysTenantVo sysTenantVo = sysTenantUpdateEvent.getSysTenantVo();
        iSysTenantService.updateByVo(sysTenantVo);
    }
}
