package com.hengjian.openapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hengjian.openapi.domain.SysApi;
import com.hengjian.openapi.domain.entity.request.OpenApiRequestEntity;
import com.hengjian.openapi.mapper.SysApiMapper;
import com.hengjian.openapi.service.IOpenApiService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
/**
 * <AUTHOR>
 * @date 2024/05/13
 * @description 恒健API开放平台统一实现类
 */
@RequiredArgsConstructor
@Service
public class OpenApiServiceImpl implements IOpenApiService {
    private static final Logger log = LoggerFactory.getLogger(OpenApiServiceImpl.class);
    private final SysApiMapper sysApiMapper;


    /**
     * API开放平台校验请求参数/签名
     * @param openApiRequestEntity API开放平台统一请求参数
     */
    public void checkAPISign(OpenApiRequestEntity openApiRequestEntity) {
        try {
            log.info("API开放平台请求参数{}", openApiRequestEntity.toString());
            // 校验时间戳
            checkExpireTime(openApiRequestEntity.getTimesTamp(), (long) (1000 * 60 * 5));
            //校验数据库
            SysApi sysApiBo = sysApiMapper.getApiByApiRequestEntity(openApiRequestEntity);
            if (ObjectUtil.isNull(sysApiBo)) {
                throw new RuntimeException("非法的请求! AppKey不存在/API请求地址未授权");
            }
            //校验签名
            String A = openApiRequestEntity.getAppKey() + openApiRequestEntity.getTenantId() + openApiRequestEntity.getTimesTamp() + openApiRequestEntity.getFormat() + openApiRequestEntity.getVersion();
            String B = JSONObject.toJSONString(openApiRequestEntity.getParam(), SerializerFeature.MapSortField);
            String original = sysApiBo.getApiSecretkey() + A + B + sysApiBo.getApiSecretkey();
            log.info(StrUtil.format("加密前A:{}", A));
            log.info(StrUtil.format("加密前B:{}", B));
            log.info(StrUtil.format("加密前C:{}", original));
            MessageDigest md = MessageDigest.getInstance("SHA-512");
            byte[] data = md.digest(original.getBytes());
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.length; i++) {
                sb.append(Integer.toString((data[i] & 0xff) + 0x100, 16).substring(1));
            }
            String sign = sb.toString().toUpperCase();
            log.info("恒健API开放平台计算的签名:  " + sign);
            if (!sign.equals(openApiRequestEntity.getSign())) {
                throw new RuntimeException("非法的请求! 签名错误!");
            }
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 判断时间戳是否过期
     *
     * @param timestamp  原始时间
     * @param expireTime 偏移时间
     * @return boolean
     */
    public void checkExpireTime(Long timestamp, Long expireTime) {
        long currentTime = System.currentTimeMillis();
        boolean isExpired = currentTime > timestamp + expireTime;
        if (isExpired) {
            throw new RuntimeException("非法的时间戳,时间戳5分钟有效!");
        }
    }



    public static void main(String[] args) throws NoSuchAlgorithmException {
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("x-api-key", "be1664e37d6650cefe1581145dd7806r");
        System.out.println(JSON.toJSONString(objectObjectHashMap));
        String s="{\"x-api-key\":\"be1664e37d6650cefe1581145dd7806r\"}";
        System.out.println(JSON.toJSONString(s));
        String appSecret = "cda71763b1ede8ef880c7ebf0bfa17076f70fd14";
        String jsonStr = "{\n" +
            "  \"tenantId\": \"D8RHYF3\",\n" +
            "  \"appKey\": \"aP77eVEnUF5b\",\n" +
            "  \"timesTamp\": 1716987093000,\n" +
            "  \"format\": \"json\",\n" +
            "  \"version\": \"1.0\",\n" +
            " \"param\": \"[\\\"HJ896035\\\",\\\"HJ390107\\\"]\",\n" +
            "  \"sign\": \"22309D21BD28222D3FF58A8309944BE2B7B82E67ACAE3C43892887EFCE10CC1E1D46EA2B89AC9DD69BE94C8761A3F1883651AFBDCF7D28268C02CE88E2AB7BF8\"\n" +
            "}";
        OpenApiRequestEntity openApiRequestEntity = JSON.parseObject(jsonStr, OpenApiRequestEntity.class);
        String A = openApiRequestEntity.getAppKey() + openApiRequestEntity.getTenantId() + openApiRequestEntity.getTimesTamp() + openApiRequestEntity.getFormat() + openApiRequestEntity.getVersion();
        System.out.println(A);
        String B = JSONObject.toJSONString(openApiRequestEntity.getParam(), SerializerFeature.MapSortField);
        System.out.println(B);
        String original = appSecret + A + B + appSecret;
        System.out.println(original);

        MessageDigest md = MessageDigest.getInstance("SHA-512");
        byte[] data = md.digest(original.getBytes());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            sb.append(Integer.toString((data[i] & 0xff) + 0x100, 16).substring(1));
        }
        String sign = sb.toString().toUpperCase();
        System.out.println(sign);
    }

    @Override
    public String getSign(OpenApiRequestEntity openApiRequestEntity,String apiSecretkey) throws NoSuchAlgorithmException {
        log.info("API开放平台请求参数{}", openApiRequestEntity.toString());
        // 校验时间戳
        checkExpireTime(openApiRequestEntity.getTimesTamp(), (long) (1000 * 60 * 5));
        //校验签名
        String A = openApiRequestEntity.getAppKey() + openApiRequestEntity.getTenantId() + openApiRequestEntity.getTimesTamp() + openApiRequestEntity.getFormat() + openApiRequestEntity.getVersion();
        String B = JSONObject.toJSONString(openApiRequestEntity.getParam(), SerializerFeature.MapSortField);
        String original = apiSecretkey + A + B + apiSecretkey;
        log.info(StrUtil.format("加密前A:{}", A));
        log.info(StrUtil.format("加密前B:{}", B));
        log.info(StrUtil.format("加密前C:{}", original));
        MessageDigest md = MessageDigest.getInstance("SHA-512");
        byte[] data = md.digest(original.getBytes());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            sb.append(Integer.toString((data[i] & 0xff) + 0x100, 16).substring(1));
        }
        return sb.toString().toUpperCase();
    }
}
